/**
 * 认证系统常量定义
 */

// IPC 消息 ID - 认证相关
export const AUTH_LOGIN_REQUEST = 'auth.login.request';
export const AUTH_LOGIN_RESPONSE = 'auth.login.response';
export const AUTH_GEETEST_REQUEST = 'auth.geetest.request';
export const AUTH_GEETEST_RESPONSE = 'auth.geetest.response';
export const AUTH_PHONE_VERIFY_REQUEST = 'auth.phoneVerify.request';
export const AUTH_PHONE_VERIFY_RESPONSE = 'auth.phoneVerify.response';
export const AUTH_STATUS_REQUEST = 'auth.status.request';
export const AUTH_STATUS_RESPONSE = 'auth.status.response';
export const AUTH_CLEAR_REQUEST = 'auth.clear.request';
export const AUTH_CLEAR_RESPONSE = 'auth.clear.response';
export const AUTH_LOGOUT_REQUEST = 'auth.logout.request';
export const AUTH_LOGOUT_RESPONSE = 'auth.logout.response';

// IPC 消息 ID - 极验相关
export const GEETEST_INIT_REQUEST = 'geetest.init.request';
export const GEETEST_INIT_RESPONSE = 'geetest.init.response';
export const GEETEST_VERIFY_REQUEST = 'geetest.verify.request';
export const GEETEST_VERIFY_RESPONSE = 'geetest.verify.response';
export const GEETEST_RESET_REQUEST = 'geetest.reset.request';
export const GEETEST_DESTROY_REQUEST = 'geetest.destroy.request';

// IPC 消息 ID - 短信相关
export const SMS_SEND_CODE_REQUEST = 'sms.sendCode.request';
export const SMS_SEND_CODE_RESPONSE = 'sms.sendCode.response';
export const SMS_VERIFY_CODE_REQUEST = 'sms.verifyCode.request';
export const SMS_VERIFY_CODE_RESPONSE = 'sms.verifyCode.response';
export const SMS_GET_COOLDOWN_REQUEST = 'sms.getCooldown.request';
export const SMS_GET_COOLDOWN_RESPONSE = 'sms.getCooldown.response';

// IPC 消息 ID - Token 相关
export const TOKEN_GET_REQUEST = 'token.get.request';
export const TOKEN_GET_RESPONSE = 'token.get.response';
export const TOKEN_REFRESH_REQUEST = 'token.refresh.request';
export const TOKEN_REFRESH_RESPONSE = 'token.refresh.response';
export const TOKEN_VALIDATE_REQUEST = 'token.validate.request';
export const TOKEN_VALIDATE_RESPONSE = 'token.validate.response';

// IPC 消息 ID - 安全相关
export const SECURITY_GET_DEVICE_ID_REQUEST = 'security.getDeviceId.request';
export const SECURITY_GET_DEVICE_ID_RESPONSE = 'security.getDeviceId.response';
export const SECURITY_CHECK_DEVICE_TRUST_REQUEST = 'security.checkDeviceTrust.request';
export const SECURITY_CHECK_DEVICE_TRUST_RESPONSE = 'security.checkDeviceTrust.response';
export const SECURITY_ADD_TRUSTED_DEVICE_REQUEST = 'security.addTrustedDevice.request';
export const SECURITY_ADD_TRUSTED_DEVICE_RESPONSE = 'security.addTrustedDevice.response';

// 认证步骤常量
export const AUTH_STEPS = {
  LOGIN: 'login',
  GEETEST: 'geetest',
  PHONE_VERIFY: 'phone_verify',
  SUCCESS: 'success'
} as const;

// 认证步骤显示名称
export const AUTH_STEP_NAMES = {
  [AUTH_STEPS.LOGIN]: '用户登录',
  [AUTH_STEPS.GEETEST]: '安全验证',
  [AUTH_STEPS.PHONE_VERIFY]: '手机验证',
  [AUTH_STEPS.SUCCESS]: '登录成功'
} as const;

// 风险等级常量
export const RISK_LEVELS = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH'
} as const;

// 风险等级显示名称
export const RISK_LEVEL_NAMES = {
  [RISK_LEVELS.LOW]: '低风险',
  [RISK_LEVELS.MEDIUM]: '中风险',
  [RISK_LEVELS.HIGH]: '高风险'
} as const;

// 认证错误类型常量
export const AUTH_ERROR_TYPES = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  GEETEST_FAILED: 'GEETEST_FAILED',
  PHONE_VERIFY_FAILED: 'PHONE_VERIFY_FAILED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  DEVICE_NOT_TRUSTED: 'DEVICE_NOT_TRUSTED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SECURITY_VIOLATION: 'SECURITY_VIOLATION',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

// 认证错误消息
export const AUTH_ERROR_MESSAGES = {
  [AUTH_ERROR_TYPES.INVALID_CREDENTIALS]: '用户名或密码错误',
  [AUTH_ERROR_TYPES.ACCOUNT_LOCKED]: '账户已被锁定，请稍后重试',
  [AUTH_ERROR_TYPES.GEETEST_FAILED]: '安全验证失败，请重试',
  [AUTH_ERROR_TYPES.PHONE_VERIFY_FAILED]: '手机验证失败，请检查验证码',
  [AUTH_ERROR_TYPES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
  [AUTH_ERROR_TYPES.DEVICE_NOT_TRUSTED]: '设备未受信任，需要额外验证',
  [AUTH_ERROR_TYPES.NETWORK_ERROR]: '网络连接异常，请检查网络',
  [AUTH_ERROR_TYPES.SECURITY_VIOLATION]: '检测到安全异常，请联系管理员',
  [AUTH_ERROR_TYPES.UNKNOWN_ERROR]: '未知错误，请稍后重试'
} as const;

// 认证事件类型常量
export const AUTH_EVENT_TYPES = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILED: 'LOGIN_FAILED',
  GEETEST_START: 'GEETEST_START',
  GEETEST_SUCCESS: 'GEETEST_SUCCESS',
  GEETEST_FAILED: 'GEETEST_FAILED',
  PHONE_VERIFY_START: 'PHONE_VERIFY_START',
  PHONE_VERIFY_SUCCESS: 'PHONE_VERIFY_SUCCESS',
  PHONE_VERIFY_FAILED: 'PHONE_VERIFY_FAILED',
  AUTH_COMPLETE: 'AUTH_COMPLETE',
  LOGOUT: 'LOGOUT',
  TOKEN_REFRESH: 'TOKEN_REFRESH',
  SECURITY_ALERT: 'SECURITY_ALERT'
} as const;

// 极验配置常量
export const GEETEST_CONFIG = {
  PRODUCT_TYPES: {
    POPUP: 'popup',
    FLOAT: 'float',
    EMBED: 'embed',
    BIND: 'bind'
  },
  LANGUAGES: {
    ZH_CN: 'zh-cn',
    EN: 'en',
    ZH_TW: 'zh-tw',
    JA: 'ja',
    KO: 'ko'
  },
  DEFAULT_TIMEOUT: 60000, // 60秒
  DEFAULT_WIDTH: '300px'
} as const;

// 短信验证常量
export const SMS_CONFIG = {
  CODE_LENGTH: 6,
  CODE_EXPIRY: 5 * 60 * 1000, // 5分钟
  COOLDOWN_TIME: 60 * 1000, // 60秒
  MAX_SEND_ATTEMPTS: 5, // 每小时最多发送5次
  SEND_WINDOW: 60 * 60 * 1000 // 1小时窗口期
} as const;

// 安全配置常量
export const SECURITY_CONFIG = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15分钟
  ATTEMPT_WINDOW: 60 * 60 * 1000, // 1小时窗口期
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24小时
  REFRESH_WINDOW: 60 * 60 * 1000, // 刷新窗口期1小时
  DEVICE_TRUST_DURATION: 30 * 24 * 60 * 60 * 1000, // 30天
  SESSION_TIMEOUT: 2 * 60 * 60 * 1000, // 2小时无操作超时
  MAX_CONCURRENT_SESSIONS: 3 // 最大并发会话数
} as const;

// 验证规则常量
export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9_-]+$/
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: false,
    REQUIRE_SYMBOLS: false
  },
  PHONE: {
    PATTERN: /^1[3-9]\d{9}$/
  },
  VERIFICATION_CODE: {
    LENGTH: 6,
    PATTERN: /^\d{6}$/
  }
} as const;

// 存储键名常量
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  DEVICE_ID: 'device_id',
  TRUSTED_DEVICES: 'trusted_devices',
  LOGIN_ATTEMPTS: 'login_attempts',
  SECURITY_EVENTS: 'security_events',
  AUTH_CONFIG: 'auth_config',
  REMEMBER_ME: 'remember_me',
  LAST_USERNAME: 'last_username'
} as const;

// API 端点常量
export const API_ENDPOINTS = {
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  REFRESH_TOKEN: '/api/auth/refresh',
  USER_INFO: '/api/auth/user',
  GEETEST_INIT: '/api/auth/geetest/init',
  GEETEST_VERIFY: '/api/auth/geetest/verify',
  SMS_SEND: '/api/auth/sms/send',
  SMS_VERIFY: '/api/auth/sms/verify',
  DEVICE_TRUST: '/api/auth/device/trust',
  SECURITY_LOG: '/api/auth/security/log'
} as const;

// 环境配置常量
export const ENV_CONFIG = {
  DEVELOPMENT: {
    API_BASE_URL: 'http://localhost:8000',
    GEETEST_GT: 'test_gt_id',
    LOG_LEVEL: 'debug'
  },
  SIT: {
    API_BASE_URL: 'http://eipdev.htsc.com.cn',
    GEETEST_GT: 'sit_gt_id',
    LOG_LEVEL: 'info'
  },
  UAT: {
    API_BASE_URL: 'http://eipsit.htsc.com.cn',
    GEETEST_GT: 'uat_gt_id',
    LOG_LEVEL: 'info'
  },
  PROD: {
    API_BASE_URL: 'http://eip.htsc.com.cn',
    GEETEST_GT: 'prod_gt_id',
    LOG_LEVEL: 'warn'
  }
} as const;

// 样式类名常量
export const CSS_CLASSES = {
  AUTH_CONTAINER: 'auth-container',
  AUTH_FORM: 'auth-form',
  AUTH_INPUT: 'auth-input',
  AUTH_BUTTON: 'auth-button',
  AUTH_ERROR: 'auth-error',
  AUTH_SUCCESS: 'auth-success',
  AUTH_LOADING: 'auth-loading',
  GEETEST_CONTAINER: 'geetest-container',
  PHONE_VERIFY_CONTAINER: 'phone-verify-container',
  AUTH_LAYOUT: 'auth-layout',
  AUTH_HEADER: 'auth-header',
  AUTH_CONTENT: 'auth-content',
  AUTH_FOOTER: 'auth-footer'
} as const;

// 动画配置常量
export const ANIMATION_CONFIG = {
  FADE_DURATION: 300,
  SLIDE_DURATION: 400,
  BOUNCE_DURATION: 600,
  LOADING_DURATION: 1000,
  ERROR_SHAKE_DURATION: 500
} as const;

// 默认配置
export const DEFAULT_AUTH_CONFIG = {
  geetestEnabled: true,
  smsEnabled: true,
  maxLoginAttempts: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS,
  lockoutDuration: SECURITY_CONFIG.LOCKOUT_DURATION,
  tokenExpiration: SECURITY_CONFIG.TOKEN_EXPIRY,
  deviceTrustEnabled: true,
  trustDeviceDuration: SECURITY_CONFIG.DEVICE_TRUST_DURATION,
  rememberMeEnabled: true,
  autoLogoutEnabled: true,
  autoLogoutDuration: SECURITY_CONFIG.SESSION_TIMEOUT
} as const;

// 正则表达式常量
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,50}$/,
  PASSWORD: /^.{6,128}$/,
  VERIFICATION_CODE: /^\d{6}$/,
  DEVICE_ID: /^[a-f0-9]{32}$/,
  SESSION_ID: /^[a-f0-9]{32}$/,
  TOKEN: /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/
} as const;

// 时间格式常量
export const TIME_FORMATS = {
  DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
} as const;

// 文件路径常量
export const FILE_PATHS = {
  AUTH_TOKENS: 'auth_tokens.json',
  SECURITY_DATA: 'security_data.json',
  USER_PREFERENCES: 'user_preferences.json',
  DEVICE_INFO: 'device_info.json',
  LOGS: 'logs'
} as const;
